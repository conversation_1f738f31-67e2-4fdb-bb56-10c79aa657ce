import { Element, Compound, CompoundElement, MixingResult, ChemicalReaction } from '@/types';
import { getElementById, isMetal, isNonmetal } from './elementUtils';

/**
 * Common compounds database
 */
const COMMON_COMPOUNDS: Compound[] = [
  {
    id: 'h2o',
    name: 'Water',
    formula: 'H₂O',
    elements: [
      { element: getElementById(1)!, count: 2 }, // Hydrogen
      { element: getElementById(8)!, count: 1 }, // Oxygen
    ],
    molecularWeight: 18.015,
    type: 'covalent',
    state: 'liquid',
    color: '#87CEEB',
    description: 'Essential for all known forms of life',
    commonName: 'Water',
    uses: ['Drinking', 'Cleaning', 'Industrial processes'],
    meltingPoint: 0,
    boilingPoint: 100,
    density: 1.0,
    solubility: 'N/A (is the universal solvent)',
  },
  {
    id: 'nacl',
    name: 'Sodium Chloride',
    formula: 'NaCl',
    elements: [
      { element: getElementById(11)!, count: 1 }, // Sodium
      { element: getElementById(17)!, count: 1 }, // Chlorine
    ],
    molecularWeight: 58.44,
    type: 'ionic',
    state: 'solid',
    color: '#FFFFFF',
    description: 'Common table salt',
    commonName: 'Salt',
    uses: ['Food seasoning', 'Food preservation', 'De-icing roads'],
    meltingPoint: 801,
    boilingPoint: 1465,
    density: 2.16,
    solubility: 'Highly soluble in water',
  },
  {
    id: 'co2',
    name: 'Carbon Dioxide',
    formula: 'CO₂',
    elements: [
      { element: getElementById(6)!, count: 1 }, // Carbon
      { element: getElementById(8)!, count: 2 }, // Oxygen
    ],
    molecularWeight: 44.01,
    type: 'covalent',
    state: 'gas',
    color: '#D3D3D3',
    description: 'Greenhouse gas produced by respiration and combustion',
    commonName: 'Carbon Dioxide',
    uses: ['Photosynthesis', 'Fire extinguishers', 'Carbonated beverages'],
    meltingPoint: -78.5,
    boilingPoint: -78.5,
    density: 0.001977,
    solubility: 'Slightly soluble in water',
  },
];

/**
 * Calculate molecular weight of a compound
 */
export function calculateMolecularWeight(elements: CompoundElement[]): number {
  return elements.reduce((total, { element, count }) => {
    return total + (element.atomicMass * count);
  }, 0);
}

/**
 * Generate chemical formula from elements
 */
export function generateFormula(elements: CompoundElement[]): string {
  return elements
    .map(({ element, count }) => {
      if (count === 1) {
        return element.symbol;
      }
      return `${element.symbol}${subscriptNumber(count)}`;
    })
    .join('');
}

/**
 * Convert number to subscript
 */
function subscriptNumber(num: number): string {
  const subscripts = ['₀', '₁', '₂', '₃', '₄', '₅', '₆', '₇', '₈', '₉'];
  return num.toString().split('').map(digit => subscripts[parseInt(digit)]).join('');
}

/**
 * Try to mix elements and create a compound
 */
export function mixElements(elements: CompoundElement[]): MixingResult {
  if (elements.length === 0) {
    return {
      success: false,
      error: 'No elements provided',
      suggestions: ['Select at least one element to mix'],
    };
  }

  if (elements.length === 1) {
    return {
      success: false,
      error: 'Need at least two elements to form a compound',
      suggestions: ['Add another element to create a compound'],
    };
  }

  // Check for known compounds
  const knownCompound = findKnownCompound(elements);
  if (knownCompound) {
    return {
      success: true,
      compound: knownCompound,
    };
  }

  // Try to predict compound formation
  const predictedCompound = predictCompound(elements);
  if (predictedCompound) {
    return {
      success: true,
      compound: predictedCompound,
    };
  }

  return {
    success: false,
    error: 'These elements do not readily form a stable compound',
    suggestions: generateMixingSuggestions(elements),
  };
}

/**
 * Find a known compound that matches the given elements
 */
function findKnownCompound(elements: CompoundElement[]): Compound | undefined {
  return COMMON_COMPOUNDS.find(compound => {
    if (compound.elements.length !== elements.length) return false;
    
    return compound.elements.every(compoundElement => {
      return elements.some(inputElement => 
        inputElement.element.atomicNumber === compoundElement.element.atomicNumber &&
        inputElement.count === compoundElement.count
      );
    });
  });
}

/**
 * Predict compound formation based on element properties
 */
function predictCompound(elements: CompoundElement[]): Compound | undefined {
  // Simple ionic compound prediction (metal + nonmetal)
  if (elements.length === 2) {
    const [first, second] = elements;
    
    if (isMetal(first.element) && isNonmetal(second.element)) {
      return createIonicCompound(first, second);
    }
    
    if (isNonmetal(first.element) && isMetal(second.element)) {
      return createIonicCompound(second, first);
    }
    
    if (isNonmetal(first.element) && isNonmetal(second.element)) {
      return createCovalentCompound(first, second);
    }
  }
  
  return undefined;
}

/**
 * Create an ionic compound
 */
function createIonicCompound(metal: CompoundElement, nonmetal: CompoundElement): Compound {
  const formula = generateFormula([metal, nonmetal]);
  const molecularWeight = calculateMolecularWeight([metal, nonmetal]);
  
  return {
    id: `${metal.element.symbol.toLowerCase()}${nonmetal.element.symbol.toLowerCase()}`,
    name: `${metal.element.name} ${nonmetal.element.name}`,
    formula,
    elements: [metal, nonmetal],
    molecularWeight,
    type: 'ionic',
    state: 'solid',
    description: `Ionic compound formed between ${metal.element.name} and ${nonmetal.element.name}`,
  };
}

/**
 * Create a covalent compound
 */
function createCovalentCompound(first: CompoundElement, second: CompoundElement): Compound {
  const formula = generateFormula([first, second]);
  const molecularWeight = calculateMolecularWeight([first, second]);
  
  return {
    id: `${first.element.symbol.toLowerCase()}${second.element.symbol.toLowerCase()}`,
    name: `${first.element.name} ${second.element.name}`,
    formula,
    elements: [first, second],
    molecularWeight,
    type: 'covalent',
    state: 'gas', // Default assumption
    description: `Covalent compound formed between ${first.element.name} and ${second.element.name}`,
  };
}

/**
 * Generate suggestions for mixing
 */
function generateMixingSuggestions(elements: CompoundElement[]): string[] {
  const suggestions: string[] = [];
  
  if (elements.length > 2) {
    suggestions.push('Try mixing only two elements at a time');
  }
  
  const hasMetals = elements.some(e => isMetal(e.element));
  const hasNonmetals = elements.some(e => isNonmetal(e.element));
  
  if (!hasMetals && !hasNonmetals) {
    suggestions.push('Try adding a metal or nonmetal element');
  } else if (hasMetals && !hasNonmetals) {
    suggestions.push('Try adding a nonmetal element to form an ionic compound');
  } else if (!hasMetals && hasNonmetals) {
    suggestions.push('Try adding a metal element to form an ionic compound');
  }
  
  suggestions.push('Experiment with different element ratios');
  suggestions.push('Check the periodic table for reactive element combinations');
  
  return suggestions;
}

/**
 * Get all known compounds
 */
export function getAllCompounds(): Compound[] {
  return [...COMMON_COMPOUNDS];
}

/**
 * Search compounds by name or formula
 */
export function searchCompounds(query: string): Compound[] {
  if (!query.trim()) return COMMON_COMPOUNDS;
  
  const searchTerm = query.toLowerCase();
  
  return COMMON_COMPOUNDS.filter(compound => 
    compound.name.toLowerCase().includes(searchTerm) ||
    compound.formula.toLowerCase().includes(searchTerm) ||
    compound.commonName?.toLowerCase().includes(searchTerm)
  );
}

/**
 * Get compound by formula
 */
export function getCompoundByFormula(formula: string): Compound | undefined {
  return COMMON_COMPOUNDS.find(compound => 
    compound.formula.toLowerCase() === formula.toLowerCase()
  );
}

/**
 * Validate element ratios for compound formation
 */
export function validateElementRatios(elements: CompoundElement[]): boolean {
  // Basic validation - all counts should be positive integers
  return elements.every(({ count }) => count > 0 && Number.isInteger(count));
}
