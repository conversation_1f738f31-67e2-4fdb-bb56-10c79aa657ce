import { Element, ElementCategory } from '@/types';

export const ELEMENT_COLORS: Record<ElementCategory, string> = {
  'alkali-metal': '#FF6B6B',
  'alkaline-earth-metal': '#4ECDC4',
  'transition-metal': '#45B7D1',
  'post-transition-metal': '#96CEB4',
  'metalloid': '#FFEAA7',
  'nonmetal': '#DDA0DD',
  'halogen': '#98D8C8',
  'noble-gas': '#F7DC6F',
  'lanthanide': '#BB8FCE',
  'actinide': '#F1948A',
  'unknown': '#BDC3C7',
};

export const PERIOD_COUNT = 7;
export const GROUP_COUNT = 18;

// Sample elements data - in a real app, this would be much more comprehensive
export const ELEMENTS: Element[] = [
  {
    atomicNumber: 1,
    symbol: 'H',
    name: 'Hydrogen',
    atomicMass: 1.008,
    electronConfiguration: '1s¹',
    electronegativity: 2.20,
    atomicRadius: 53,
    ionizationEnergy: 1312,
    oxidationStates: [-1, 1],
    standardState: 'gas',
    meltingPoint: 14.01,
    boilingPoint: 20.28,
    density: 0.00008988,
    groupBlock: 's-block',
    yearDiscovered: 1766,
    category: 'nonmetal',
    color: ELEMENT_COLORS.nonmetal,
    description: 'The lightest and most abundant element in the universe.',
  },
  {
    atomicNumber: 2,
    symbol: 'He',
    name: 'Helium',
    atomicMass: 4.003,
    electronConfiguration: '1s²',
    atomicRadius: 31,
    ionizationEnergy: 2372,
    oxidationStates: [0],
    standardState: 'gas',
    meltingPoint: 0.95,
    boilingPoint: 4.22,
    density: 0.0001785,
    groupBlock: 's-block',
    yearDiscovered: 1868,
    category: 'noble-gas',
    color: ELEMENT_COLORS['noble-gas'],
    description: 'A noble gas that is chemically inert under standard conditions.',
  },
  {
    atomicNumber: 3,
    symbol: 'Li',
    name: 'Lithium',
    atomicMass: 6.94,
    electronConfiguration: '[He] 2s¹',
    electronegativity: 0.98,
    atomicRadius: 167,
    ionizationEnergy: 520,
    oxidationStates: [1],
    standardState: 'solid',
    meltingPoint: 453.65,
    boilingPoint: 1615,
    density: 0.534,
    groupBlock: 's-block',
    yearDiscovered: 1817,
    category: 'alkali-metal',
    color: ELEMENT_COLORS['alkali-metal'],
    description: 'The lightest metal and the least dense solid element.',
  },
  {
    atomicNumber: 6,
    symbol: 'C',
    name: 'Carbon',
    atomicMass: 12.011,
    electronConfiguration: '[He] 2s² 2p²',
    electronegativity: 2.55,
    atomicRadius: 67,
    ionizationEnergy: 1086,
    oxidationStates: [-4, -3, -2, -1, 0, 1, 2, 3, 4],
    standardState: 'solid',
    meltingPoint: 3823,
    boilingPoint: 4098,
    density: 2.267,
    groupBlock: 'p-block',
    yearDiscovered: -3750,
    category: 'nonmetal',
    color: ELEMENT_COLORS.nonmetal,
    description: 'The basis of all organic chemistry and life on Earth.',
  },
  {
    atomicNumber: 8,
    symbol: 'O',
    name: 'Oxygen',
    atomicMass: 15.999,
    electronConfiguration: '[He] 2s² 2p⁴',
    electronegativity: 3.44,
    atomicRadius: 48,
    ionizationEnergy: 1314,
    oxidationStates: [-2, -1, 0, 1, 2],
    standardState: 'gas',
    meltingPoint: 54.36,
    boilingPoint: 90.20,
    density: 0.001429,
    groupBlock: 'p-block',
    yearDiscovered: 1774,
    category: 'nonmetal',
    color: ELEMENT_COLORS.nonmetal,
    description: 'Essential for respiration and combustion.',
  },
  {
    atomicNumber: 11,
    symbol: 'Na',
    name: 'Sodium',
    atomicMass: 22.990,
    electronConfiguration: '[Ne] 3s¹',
    electronegativity: 0.93,
    atomicRadius: 190,
    ionizationEnergy: 496,
    oxidationStates: [1],
    standardState: 'solid',
    meltingPoint: 370.87,
    boilingPoint: 1156,
    density: 0.971,
    groupBlock: 's-block',
    yearDiscovered: 1807,
    category: 'alkali-metal',
    color: ELEMENT_COLORS['alkali-metal'],
    description: 'A highly reactive metal that is essential for life.',
  },
  {
    atomicNumber: 17,
    symbol: 'Cl',
    name: 'Chlorine',
    atomicMass: 35.45,
    electronConfiguration: '[Ne] 3s² 3p⁵',
    electronegativity: 3.16,
    atomicRadius: 79,
    ionizationEnergy: 1251,
    oxidationStates: [-1, 1, 3, 5, 7],
    standardState: 'gas',
    meltingPoint: 171.6,
    boilingPoint: 239.11,
    density: 0.003214,
    groupBlock: 'p-block',
    yearDiscovered: 1774,
    category: 'halogen',
    color: ELEMENT_COLORS.halogen,
    description: 'A highly reactive halogen used in water purification.',
  },
];

export const ELEMENT_POSITIONS: Record<number, { period: number; group: number }> = {
  1: { period: 1, group: 1 },
  2: { period: 1, group: 18 },
  3: { period: 2, group: 1 },
  4: { period: 2, group: 2 },
  5: { period: 2, group: 13 },
  6: { period: 2, group: 14 },
  7: { period: 2, group: 15 },
  8: { period: 2, group: 16 },
  9: { period: 2, group: 17 },
  10: { period: 2, group: 18 },
  11: { period: 3, group: 1 },
  12: { period: 3, group: 2 },
  13: { period: 3, group: 13 },
  14: { period: 3, group: 14 },
  15: { period: 3, group: 15 },
  16: { period: 3, group: 16 },
  17: { period: 3, group: 17 },
  18: { period: 3, group: 18 },
};
