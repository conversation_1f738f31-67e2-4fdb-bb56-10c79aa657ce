import { Element } from './element';

export interface CompoundElement {
  element: Element;
  count: number;
}

export interface Compound {
  id: string;
  name: string;
  formula: string;
  elements: CompoundElement[];
  molecularWeight: number;
  type: CompoundType;
  state: 'solid' | 'liquid' | 'gas' | 'aqueous';
  color?: string;
  description?: string;
  commonName?: string;
  uses?: string[];
  hazards?: string[];
  meltingPoint?: number; // in Celsius
  boilingPoint?: number; // in Celsius
  density?: number; // g/cm³
  solubility?: string;
  structure?: string; // URL to structure image or SVG
}

export type CompoundType = 
  | 'ionic'
  | 'covalent'
  | 'metallic'
  | 'acid'
  | 'base'
  | 'salt'
  | 'oxide'
  | 'organic'
  | 'inorganic';

export interface MixingResult {
  success: boolean;
  compound?: Compound;
  reaction?: ChemicalReaction;
  error?: string;
  suggestions?: string[];
}

export interface ChemicalReaction {
  id: string;
  reactants: CompoundElement[];
  products: CompoundElement[];
  reactionType: ReactionType;
  balanced: boolean;
  equation: string;
  description?: string;
  conditions?: string[];
  energy?: number; // kJ/mol (positive for endothermic, negative for exothermic)
}

export type ReactionType = 
  | 'synthesis'
  | 'decomposition'
  | 'single-replacement'
  | 'double-replacement'
  | 'combustion'
  | 'acid-base'
  | 'redox'
  | 'precipitation';

export interface MixingAttempt {
  id: string;
  elements: CompoundElement[];
  timestamp: Date;
  result: MixingResult;
  userId?: string;
}
