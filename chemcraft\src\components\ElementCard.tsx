'use client';

import { Element } from '@/types';
import { getElementColor, formatAtomicMass } from '@/lib';

interface ElementCardProps {
  element: Element;
  size?: 'small' | 'medium' | 'large';
  variant?: 'default' | 'compact' | 'detailed';
  onClick?: (element: Element) => void;
  isSelected?: boolean;
  isDisabled?: boolean;
  showDetails?: boolean;
}

export function ElementCard({
  element,
  size = 'medium',
  variant = 'default',
  onClick,
  isSelected = false,
  isDisabled = false,
  showDetails = false,
}: ElementCardProps) {
  const color = getElementColor(element);
  
  const sizeClasses = {
    small: 'w-12 h-12 text-xs',
    medium: 'w-16 h-16 text-sm',
    large: 'w-20 h-20 text-base',
  };

  const handleClick = () => {
    if (!isDisabled && onClick) {
      onClick(element);
    }
  };

  if (variant === 'compact') {
    return (
      <button
        onClick={handleClick}
        disabled={isDisabled}
        className={`
          ${sizeClasses[size]}
          relative rounded-lg border-2 transition-all duration-200
          ${isSelected 
            ? 'border-blue-500 ring-2 ring-blue-200 dark:ring-blue-800' 
            : 'border-gray-300 dark:border-gray-600'
          }
          ${isDisabled 
            ? 'opacity-50 cursor-not-allowed' 
            : 'hover:border-gray-400 dark:hover:border-gray-500 cursor-pointer hover:scale-105'
          }
          flex flex-col items-center justify-center
          text-white font-bold shadow-sm hover:shadow-md
        `}
        style={{ backgroundColor: color }}
        title={`${element.name} (${element.symbol})`}
      >
        <span className="text-xs leading-none">{element.atomicNumber}</span>
        <span className="text-lg leading-none">{element.symbol}</span>
      </button>
    );
  }

  if (variant === 'detailed') {
    return (
      <div
        onClick={handleClick}
        className={`
          p-4 rounded-lg border-2 transition-all duration-200 cursor-pointer
          ${isSelected 
            ? 'border-blue-500 ring-2 ring-blue-200 dark:ring-blue-800' 
            : 'border-gray-300 dark:border-gray-600'
          }
          ${isDisabled 
            ? 'opacity-50 cursor-not-allowed' 
            : 'hover:border-gray-400 dark:hover:border-gray-500 hover:scale-105'
          }
          bg-white dark:bg-gray-800 shadow-sm hover:shadow-md
        `}
      >
        <div className="flex items-center space-x-3">
          <div
            className="w-12 h-12 rounded-lg flex flex-col items-center justify-center text-white font-bold text-xs"
            style={{ backgroundColor: color }}
          >
            <span className="text-xs leading-none">{element.atomicNumber}</span>
            <span className="text-lg leading-none">{element.symbol}</span>
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="text-sm font-medium text-gray-900 dark:text-white truncate">
              {element.name}
            </h3>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              {formatAtomicMass(element.atomicMass)} u
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400 capitalize">
              {element.category.replace('-', ' ')}
            </p>
          </div>
        </div>
        
        {showDetails && (
          <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div>
                <span className="text-gray-500 dark:text-gray-400">State:</span>
                <span className="ml-1 capitalize text-gray-900 dark:text-white">
                  {element.standardState}
                </span>
              </div>
              {element.electronegativity && (
                <div>
                  <span className="text-gray-500 dark:text-gray-400">EN:</span>
                  <span className="ml-1 text-gray-900 dark:text-white">
                    {element.electronegativity}
                  </span>
                </div>
              )}
              <div className="col-span-2">
                <span className="text-gray-500 dark:text-gray-400">Config:</span>
                <span className="ml-1 text-gray-900 dark:text-white text-xs">
                  {element.electronConfiguration}
                </span>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  }

  // Default variant
  return (
    <button
      onClick={handleClick}
      disabled={isDisabled}
      className={`
        ${sizeClasses[size]}
        relative rounded-lg border-2 transition-all duration-200
        ${isSelected 
          ? 'border-blue-500 ring-2 ring-blue-200 dark:ring-blue-800' 
          : 'border-gray-300 dark:border-gray-600'
        }
        ${isDisabled 
          ? 'opacity-50 cursor-not-allowed' 
          : 'hover:border-gray-400 dark:hover:border-gray-500 cursor-pointer hover:scale-105'
        }
        flex flex-col items-center justify-center
        text-white font-bold shadow-sm hover:shadow-md
        group
      `}
      style={{ backgroundColor: color }}
      title={`${element.name} (${element.symbol}) - Atomic Number: ${element.atomicNumber}`}
    >
      <span className="text-xs leading-none opacity-90">{element.atomicNumber}</span>
      <span className="text-xl leading-none">{element.symbol}</span>
      <span className="text-xs leading-none opacity-90 truncate w-full px-1">
        {element.name}
      </span>
      
      {/* Hover tooltip */}
      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
        {element.name} ({formatAtomicMass(element.atomicMass)} u)
      </div>
    </button>
  );
}
