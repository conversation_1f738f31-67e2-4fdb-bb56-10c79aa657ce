import { Element, <PERSON>ement<PERSON>ate<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ElementWithPosition } from '@/types';
import { ELEMENTS, ELEMENT_POSITIONS, ELEMENT_COLORS } from '@/constants';

/**
 * Get element by atomic number
 */
export function getElementById(atomicNumber: number): Element | undefined {
  return ELEMENTS.find(element => element.atomicNumber === atomicNumber);
}

/**
 * Get element by symbol
 */
export function getElementBySymbol(symbol: string): Element | undefined {
  return ELEMENTS.find(element => element.symbol.toLowerCase() === symbol.toLowerCase());
}

/**
 * Get element by name
 */
export function getElementByName(name: string): Element | undefined {
  return ELEMENTS.find(element => element.name.toLowerCase() === name.toLowerCase());
}

/**
 * Get elements by category
 */
export function getElementsByCategory(category: ElementCategory): Element[] {
  return ELEMENTS.filter(element => element.category === category);
}

/**
 * Get elements by period
 */
export function getElementsByPeriod(period: number): ElementWithPosition[] {
  return ELEMENTS
    .map(element => ({
      ...element,
      position: ELEMENT_POSITIONS[element.atomicNumber] || { period: 0, group: 0 }
    }))
    .filter(element => element.position.period === period);
}

/**
 * Get elements by group
 */
export function getElementsByGroup(group: number): ElementWithPosition[] {
  return ELEMENTS
    .map(element => ({
      ...element,
      position: ELEMENT_POSITIONS[element.atomicNumber] || { period: 0, group: 0 }
    }))
    .filter(element => element.position.group === group);
}

/**
 * Filter elements based on criteria
 */
export function filterElements(filter: ElementFilter): Element[] {
  return ELEMENTS.filter(element => {
    const position = ELEMENT_POSITIONS[element.atomicNumber];
    
    if (filter.category && element.category !== filter.category) {
      return false;
    }
    
    if (filter.state && element.standardState !== filter.state) {
      return false;
    }
    
    if (filter.period && position?.period !== filter.period) {
      return false;
    }
    
    if (filter.group && position?.group !== filter.group) {
      return false;
    }
    
    if (filter.searchTerm) {
      const searchLower = filter.searchTerm.toLowerCase();
      const matchesName = element.name.toLowerCase().includes(searchLower);
      const matchesSymbol = element.symbol.toLowerCase().includes(searchLower);
      const matchesNumber = element.atomicNumber.toString().includes(searchLower);
      
      if (!matchesName && !matchesSymbol && !matchesNumber) {
        return false;
      }
    }
    
    return true;
  });
}

/**
 * Search elements by query
 */
export function searchElements(query: string): Element[] {
  if (!query.trim()) return ELEMENTS;
  
  const searchTerm = query.toLowerCase().trim();
  
  return ELEMENTS.filter(element => {
    return (
      element.name.toLowerCase().includes(searchTerm) ||
      element.symbol.toLowerCase().includes(searchTerm) ||
      element.atomicNumber.toString().includes(searchTerm) ||
      element.category.toLowerCase().includes(searchTerm)
    );
  });
}

/**
 * Get element color based on category
 */
export function getElementColor(element: Element): string {
  return element.color || ELEMENT_COLORS[element.category] || ELEMENT_COLORS.unknown;
}

/**
 * Format atomic mass for display
 */
export function formatAtomicMass(mass: number): string {
  return mass.toFixed(3);
}

/**
 * Format electron configuration for display
 */
export function formatElectronConfiguration(config: string): string {
  return config.replace(/\^(\d+)/g, '<sup>$1</sup>');
}

/**
 * Get element position in periodic table
 */
export function getElementPosition(atomicNumber: number): { period: number; group: number } | null {
  return ELEMENT_POSITIONS[atomicNumber] || null;
}

/**
 * Check if element is a metal
 */
export function isMetal(element: Element): boolean {
  return [
    'alkali-metal',
    'alkaline-earth-metal',
    'transition-metal',
    'post-transition-metal'
  ].includes(element.category);
}

/**
 * Check if element is a nonmetal
 */
export function isNonmetal(element: Element): boolean {
  return ['nonmetal', 'halogen', 'noble-gas'].includes(element.category);
}

/**
 * Check if element is a metalloid
 */
export function isMetalloid(element: Element): boolean {
  return element.category === 'metalloid';
}

/**
 * Get element statistics
 */
export function getElementStats() {
  const totalElements = ELEMENTS.length;
  const categories = ELEMENTS.reduce((acc, element) => {
    acc[element.category] = (acc[element.category] || 0) + 1;
    return acc;
  }, {} as Record<ElementCategory, number>);
  
  const states = ELEMENTS.reduce((acc, element) => {
    acc[element.standardState] = (acc[element.standardState] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);
  
  return {
    totalElements,
    categories,
    states,
    metals: ELEMENTS.filter(isMetal).length,
    nonmetals: ELEMENTS.filter(isNonmetal).length,
    metalloids: ELEMENTS.filter(isMetalloid).length,
  };
}

/**
 * Generate random element for quizzes
 */
export function getRandomElement(): Element {
  const randomIndex = Math.floor(Math.random() * ELEMENTS.length);
  return ELEMENTS[randomIndex];
}

/**
 * Generate random elements by category
 */
export function getRandomElementsByCategory(category: ElementCategory, count: number = 1): Element[] {
  const categoryElements = getElementsByCategory(category);
  const shuffled = [...categoryElements].sort(() => Math.random() - 0.5);
  return shuffled.slice(0, count);
}
