import { useState, useEffect, useCallback, useRef } from 'react';
import { QuizQuestion, QuizAnswer, QuizSettings } from '@/types';
import { useQuizStore } from '@/stores';
import { checkAnswer, formatTime } from '@/lib';

interface UseQuizEngineOptions {
  autoStart?: boolean;
  onQuestionChange?: (questionIndex: number, question: QuizQuestion) => void;
  onAnswerSubmit?: (answer: QuizAnswer) => void;
  onQuizComplete?: (score: number, percentage: number) => void;
  onTimeUp?: () => void;
}

export function useQuizEngine(options: UseQuizEngineOptions = {}) {
  const {
    autoStart = false,
    onQuestionChange,
    onAnswerSubmit,
    onQuizComplete,
    onTimeUp,
  } = options;

  const {
    currentSession,
    currentQuestionIndex,
    currentAnswer,
    timeRemaining,
    hintsUsed,
    settings,
    showResults,
    showHint,
    isLoading,
    startQuiz,
    answerQuestion,
    nextQuestion,
    useHint,
    endQuiz,
    restartQuiz,
    updateTimer,
    updateSettings,
  } = useQuizStore();

  const [isTimerActive, setIsTimerActive] = useState(false);
  const [userAnswer, setUserAnswer] = useState<string | number>('');
  const [isAnswered, setIsAnswered] = useState(false);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // Current question
  const currentQuestion = currentSession?.questions[currentQuestionIndex] || null;
  const totalQuestions = currentSession?.questions.length || 0;
  const progress = totalQuestions > 0 ? ((currentQuestionIndex + 1) / totalQuestions) * 100 : 0;

  // Quiz state
  const isQuizActive = !!currentSession && !showResults;
  const isLastQuestion = currentQuestionIndex === totalQuestions - 1;
  const canUseHints = settings.hintsEnabled && currentQuestion?.hints && currentQuestion.hints.length > 0;

  // Timer management
  const startTimer = useCallback(() => {
    if (!currentQuestion?.timeLimit && !settings.timeLimit) return;
    
    setIsTimerActive(true);
    const timeLimit = currentQuestion?.timeLimit || settings.timeLimit || 60;
    updateTimer(timeLimit);

    timerRef.current = setInterval(() => {
      updateTimer((prev) => {
        const newTime = prev - 1;
        if (newTime <= 0) {
          setIsTimerActive(false);
          onTimeUp?.();
          return 0;
        }
        return newTime;
      });
    }, 1000);
  }, [currentQuestion, settings.timeLimit, updateTimer, onTimeUp]);

  const stopTimer = useCallback(() => {
    setIsTimerActive(false);
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  }, []);

  const resetTimer = useCallback(() => {
    stopTimer();
    if (currentQuestion?.timeLimit || settings.timeLimit) {
      startTimer();
    }
  }, [currentQuestion, settings.timeLimit, startTimer, stopTimer]);

  // Quiz actions
  const handleStartQuiz = useCallback((customSettings?: Partial<QuizSettings>) => {
    startQuiz(customSettings);
    setUserAnswer('');
    setIsAnswered(false);
  }, [startQuiz]);

  const handleAnswerSubmit = useCallback(() => {
    if (!currentQuestion || isAnswered || !userAnswer) return;

    const isCorrect = checkAnswer(currentQuestion, userAnswer);
    answerQuestion(userAnswer);
    
    const answer: QuizAnswer = {
      questionId: currentQuestion.id,
      userAnswer,
      isCorrect,
      timeSpent: (currentQuestion.timeLimit || settings.timeLimit || 60) - timeRemaining,
      hintsUsed,
      timestamp: new Date(),
    };

    setIsAnswered(true);
    stopTimer();
    onAnswerSubmit?.(answer);

    // Auto-advance after a delay
    setTimeout(() => {
      handleNextQuestion();
    }, 2000);
  }, [
    currentQuestion,
    isAnswered,
    userAnswer,
    answerQuestion,
    timeRemaining,
    hintsUsed,
    settings.timeLimit,
    stopTimer,
    onAnswerSubmit,
  ]);

  const handleNextQuestion = useCallback(() => {
    if (isLastQuestion) {
      endQuiz();
      const session = currentSession;
      if (session) {
        onQuizComplete?.(session.score, session.percentage);
      }
    } else {
      nextQuestion();
      setUserAnswer('');
      setIsAnswered(false);
      resetTimer();
    }
  }, [isLastQuestion, endQuiz, nextQuestion, currentSession, onQuizComplete, resetTimer]);

  const handleUseHint = useCallback(() => {
    if (!canUseHints || showHint) return;
    useHint();
  }, [canUseHints, showHint, useHint]);

  const handleSkipQuestion = useCallback(() => {
    if (!currentQuestion) return;
    
    // Submit empty answer
    answerQuestion('');
    setIsAnswered(true);
    stopTimer();
    
    setTimeout(() => {
      handleNextQuestion();
    }, 1000);
  }, [currentQuestion, answerQuestion, stopTimer, handleNextQuestion]);

  // Effects
  useEffect(() => {
    if (autoStart && !currentSession) {
      handleStartQuiz();
    }
  }, [autoStart, currentSession, handleStartQuiz]);

  useEffect(() => {
    if (currentQuestion && !isAnswered) {
      onQuestionChange?.(currentQuestionIndex, currentQuestion);
      resetTimer();
    }
  }, [currentQuestion, currentQuestionIndex, isAnswered, onQuestionChange, resetTimer]);

  useEffect(() => {
    if (timeRemaining <= 0 && isTimerActive) {
      handleSkipQuestion();
    }
  }, [timeRemaining, isTimerActive, handleSkipQuestion]);

  // Cleanup timer on unmount
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);

  // Helper functions
  const getHintText = () => {
    if (!currentQuestion?.hints || hintsUsed >= currentQuestion.hints.length) {
      return null;
    }
    return currentQuestion.hints[hintsUsed];
  };

  const getQuestionProgress = () => {
    return {
      current: currentQuestionIndex + 1,
      total: totalQuestions,
      percentage: progress,
    };
  };

  const getTimeDisplay = () => {
    return formatTime(timeRemaining);
  };

  const isAnswerCorrect = () => {
    if (!currentQuestion || !isAnswered) return null;
    return checkAnswer(currentQuestion, userAnswer);
  };

  return {
    // Quiz state
    currentSession,
    currentQuestion,
    currentQuestionIndex,
    totalQuestions,
    isQuizActive,
    isLastQuestion,
    showResults,
    isLoading,
    
    // Answer state
    userAnswer,
    setUserAnswer,
    isAnswered,
    isAnswerCorrect: isAnswerCorrect(),
    
    // Timer state
    timeRemaining,
    isTimerActive,
    timeDisplay: getTimeDisplay(),
    
    // Hints
    hintsUsed,
    canUseHints,
    showHint,
    hintText: getHintText(),
    
    // Progress
    progress,
    questionProgress: getQuestionProgress(),
    
    // Actions
    startQuiz: handleStartQuiz,
    submitAnswer: handleAnswerSubmit,
    nextQuestion: handleNextQuestion,
    useHint: handleUseHint,
    skipQuestion: handleSkipQuestion,
    endQuiz,
    restartQuiz,
    
    // Settings
    settings,
    updateSettings,
    
    // Utilities
    canSubmit: !!userAnswer && !isAnswered,
    canSkip: !isAnswered,
    canNext: isAnswered,
  };
}
