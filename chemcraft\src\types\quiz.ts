import { Element } from './element';
import { Compound } from './compound';

export interface QuizQuestion {
  id: string;
  type: QuestionType;
  question: string;
  options?: string[];
  correctAnswer: string | number;
  explanation?: string;
  difficulty: 'easy' | 'medium' | 'hard';
  category: QuizCategory;
  points: number;
  timeLimit?: number; // in seconds
  hints?: string[];
  relatedElement?: Element;
  relatedCompound?: Compound;
}

export type QuestionType = 
  | 'multiple-choice'
  | 'true-false'
  | 'fill-in-blank'
  | 'numeric'
  | 'element-identification'
  | 'formula-writing'
  | 'equation-balancing';

export type QuizCategory = 
  | 'periodic-table'
  | 'atomic-structure'
  | 'chemical-bonding'
  | 'compounds'
  | 'reactions'
  | 'stoichiometry'
  | 'thermodynamics'
  | 'general';

export interface QuizAnswer {
  questionId: string;
  userAnswer: string | number;
  isCorrect: boolean;
  timeSpent: number; // in seconds
  hintsUsed: number;
  timestamp: Date;
}

export interface QuizSession {
  id: string;
  questions: QuizQuestion[];
  answers: QuizAnswer[];
  startTime: Date;
  endTime?: Date;
  score: number;
  maxScore: number;
  percentage: number;
  timeSpent: number; // total time in seconds
  difficulty: 'easy' | 'medium' | 'hard' | 'mixed';
  category: QuizCategory | 'mixed';
  completed: boolean;
  userId?: string;
}

export interface QuizStats {
  totalQuizzes: number;
  averageScore: number;
  bestScore: number;
  totalTimeSpent: number;
  favoriteCategory: QuizCategory;
  strongestCategory: QuizCategory;
  weakestCategory: QuizCategory;
  streakCount: number;
  achievements: Achievement[];
}

export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  unlockedAt?: Date;
  progress?: number;
  maxProgress?: number;
}

export interface QuizSettings {
  questionCount: number;
  difficulty: 'easy' | 'medium' | 'hard' | 'mixed';
  category: QuizCategory | 'mixed';
  timeLimit?: number; // per question in seconds
  hintsEnabled: boolean;
  randomOrder: boolean;
}
