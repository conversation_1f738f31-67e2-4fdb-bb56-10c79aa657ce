import { useState, useEffect, useMemo } from 'react';
import { Element, ElementWithPosition } from '@/types';
import { 
  getElementById, 
  getElementBySymbol, 
  getElementPosition, 
  getElementColor,
  formatAtomicMass,
  formatElectronConfiguration,
  isMetal,
  isNonmetal,
  isMetalloid
} from '@/lib';

interface ElementDetails extends ElementWithPosition {
  formattedMass: string;
  formattedElectronConfig: string;
  colorCode: string;
  classification: 'metal' | 'nonmetal' | 'metalloid';
  isRadioactive: boolean;
  electronShells: number[];
}

interface UseElementDetailsOptions {
  autoLoad?: boolean;
  includePosition?: boolean;
}

export function useElementDetails(
  elementId?: number | string,
  options: UseElementDetailsOptions = {}
) {
  const { autoLoad = true, includePosition = true } = options;
  
  const [element, setElement] = useState<Element | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load element data
  const loadElement = async (id: number | string) => {
    if (!id) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      let foundElement: Element | undefined;
      
      if (typeof id === 'number') {
        foundElement = getElementById(id);
      } else {
        foundElement = getElementBySymbol(id);
      }
      
      if (!foundElement) {
        throw new Error(`Element not found: ${id}`);
      }
      
      setElement(foundElement);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load element');
      setElement(null);
    } finally {
      setIsLoading(false);
    }
  };

  // Auto-load element when elementId changes
  useEffect(() => {
    if (autoLoad && elementId) {
      loadElement(elementId);
    }
  }, [elementId, autoLoad]);

  // Calculate electron shells from electron configuration
  const calculateElectronShells = (electronConfig: string): number[] => {
    const shells: number[] = [];
    const regex = /(\d+)s(\d+)|(\d+)p(\d+)|(\d+)d(\d+)|(\d+)f(\d+)/g;
    let match;
    
    while ((match = regex.exec(electronConfig)) !== null) {
      const shell = parseInt(match[1] || match[3] || match[5] || match[7]);
      const electrons = parseInt(match[2] || match[4] || match[6] || match[8]);
      
      while (shells.length < shell) {
        shells.push(0);
      }
      shells[shell - 1] += electrons;
    }
    
    return shells;
  };

  // Enhanced element details with computed properties
  const elementDetails: ElementDetails | null = useMemo(() => {
    if (!element) return null;
    
    const position = includePosition ? getElementPosition(element.atomicNumber) : null;
    const electronShells = calculateElectronShells(element.electronConfiguration);
    
    let classification: 'metal' | 'nonmetal' | 'metalloid';
    if (isMetal(element)) classification = 'metal';
    else if (isNonmetal(element)) classification = 'nonmetal';
    else classification = 'metalloid';
    
    return {
      ...element,
      position: position || { period: 0, group: 0 },
      formattedMass: formatAtomicMass(element.atomicMass),
      formattedElectronConfig: formatElectronConfiguration(element.electronConfiguration),
      colorCode: getElementColor(element),
      classification,
      isRadioactive: element.atomicNumber > 83, // Simple heuristic
      electronShells,
    };
  }, [element, includePosition]);

  // Helper functions
  const getElementProperty = (property: keyof Element) => {
    return element?.[property];
  };

  const compareWithElement = (otherElementId: number | string) => {
    if (!element) return null;
    
    let otherElement: Element | undefined;
    if (typeof otherElementId === 'number') {
      otherElement = getElementById(otherElementId);
    } else {
      otherElement = getElementBySymbol(otherElementId);
    }
    
    if (!otherElement) return null;
    
    return {
      atomicNumber: {
        current: element.atomicNumber,
        other: otherElement.atomicNumber,
        difference: element.atomicNumber - otherElement.atomicNumber,
      },
      atomicMass: {
        current: element.atomicMass,
        other: otherElement.atomicMass,
        difference: element.atomicMass - otherElement.atomicMass,
      },
      electronegativity: element.electronegativity && otherElement.electronegativity ? {
        current: element.electronegativity,
        other: otherElement.electronegativity,
        difference: element.electronegativity - otherElement.electronegativity,
      } : null,
    };
  };

  const getPeriodicTrends = () => {
    if (!elementDetails?.position) return null;
    
    const { period, group } = elementDetails.position;
    
    return {
      period,
      group,
      trends: {
        atomicRadius: {
          acrossPeriod: 'decreases',
          downGroup: 'increases',
        },
        ionizationEnergy: {
          acrossPeriod: 'increases',
          downGroup: 'decreases',
        },
        electronegativity: {
          acrossPeriod: 'increases',
          downGroup: 'decreases',
        },
      },
    };
  };

  return {
    // Data
    element,
    elementDetails,
    
    // State
    isLoading,
    error,
    
    // Actions
    loadElement,
    refetch: () => elementId && loadElement(elementId),
    
    // Utilities
    getElementProperty,
    compareWithElement,
    getPeriodicTrends,
  };
}
