import { QuizQ<PERSON><PERSON>, QuizAnswer, QuizSession, QuizSettings, QuizCategory, Achievement } from '@/types';
import { SAMPLE_QUIZ_QUESTIONS, ACHIEVEMENTS_DATA } from '@/constants';
import { getRandomElement, getRandomElementsByCategory } from './elementUtils';

/**
 * Generate a quiz based on settings
 */
export function generateQuiz(settings: QuizSettings): QuizQuestion[] {
  let availableQuestions = [...SAMPLE_QUIZ_QUESTIONS];
  
  // Filter by category
  if (settings.category !== 'mixed') {
    availableQuestions = availableQuestions.filter(q => q.category === settings.category);
  }
  
  // Filter by difficulty
  if (settings.difficulty !== 'mixed') {
    availableQuestions = availableQuestions.filter(q => q.difficulty === settings.difficulty);
  }
  
  // Shuffle questions if randomOrder is enabled
  if (settings.randomOrder) {
    availableQuestions = shuffleArray(availableQuestions);
  }
  
  // Take the requested number of questions
  const selectedQuestions = availableQuestions.slice(0, settings.questionCount);
  
  // If we don't have enough questions, generate some dynamically
  if (selectedQuestions.length < settings.questionCount) {
    const additionalQuestions = generateDynamicQuestions(
      settings.questionCount - selectedQuestions.length,
      settings.category,
      settings.difficulty
    );
    selectedQuestions.push(...additionalQuestions);
  }
  
  return selectedQuestions;
}

/**
 * Generate dynamic questions when we don't have enough static ones
 */
function generateDynamicQuestions(
  count: number,
  category: QuizCategory | 'mixed',
  difficulty: 'easy' | 'medium' | 'hard' | 'mixed'
): QuizQuestion[] {
  const questions: QuizQuestion[] = [];
  
  for (let i = 0; i < count; i++) {
    const questionCategory = category === 'mixed' ? getRandomCategory() : category;
    const questionDifficulty = difficulty === 'mixed' ? getRandomDifficulty() : difficulty;
    
    const question = generateElementIdentificationQuestion(questionCategory, questionDifficulty);
    questions.push(question);
  }
  
  return questions;
}

/**
 * Generate an element identification question
 */
function generateElementIdentificationQuestion(
  category: QuizCategory,
  difficulty: 'easy' | 'medium' | 'hard'
): QuizQuestion {
  const element = getRandomElement();
  const questionId = `dynamic_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  
  // Generate wrong options
  const wrongElements = getRandomElementsByCategory(element.category, 3);
  const options = [element.name, ...wrongElements.map(e => e.name)];
  const shuffledOptions = shuffleArray(options);
  
  return {
    id: questionId,
    type: 'multiple-choice',
    question: `What is the name of the element with symbol "${element.symbol}"?`,
    options: shuffledOptions,
    correctAnswer: element.name,
    explanation: `${element.symbol} is the symbol for ${element.name}, which has atomic number ${element.atomicNumber}.`,
    difficulty,
    category: 'periodic-table',
    points: getDifficultyPoints(difficulty),
    timeLimit: getDifficultyTimeLimit(difficulty),
    relatedElement: element,
  };
}

/**
 * Calculate quiz score
 */
export function calculateQuizScore(answers: QuizAnswer[]): {
  score: number;
  maxScore: number;
  percentage: number;
  correctAnswers: number;
  totalAnswers: number;
} {
  const correctAnswers = answers.filter(answer => answer.isCorrect).length;
  const totalAnswers = answers.length;
  const score = answers.reduce((total, answer) => {
    return total + (answer.isCorrect ? getAnswerPoints(answer) : 0);
  }, 0);
  const maxScore = answers.reduce((total, answer) => {
    return total + getAnswerPoints(answer);
  }, 0);
  const percentage = maxScore > 0 ? Math.round((score / maxScore) * 100) : 0;
  
  return {
    score,
    maxScore,
    percentage,
    correctAnswers,
    totalAnswers,
  };
}

/**
 * Get points for an answer based on question difficulty and hints used
 */
function getAnswerPoints(answer: QuizAnswer): number {
  const basePoints = getDifficultyPoints('medium'); // Default if we can't determine
  const hintsUsed = answer.hintsUsed || 0;
  const pointsReduction = hintsUsed * 0.1; // 10% reduction per hint
  
  return Math.max(1, Math.round(basePoints * (1 - pointsReduction)));
}

/**
 * Get points based on difficulty
 */
function getDifficultyPoints(difficulty: 'easy' | 'medium' | 'hard'): number {
  switch (difficulty) {
    case 'easy': return 1;
    case 'medium': return 2;
    case 'hard': return 3;
    default: return 2;
  }
}

/**
 * Get time limit based on difficulty
 */
function getDifficultyTimeLimit(difficulty: 'easy' | 'medium' | 'hard'): number {
  switch (difficulty) {
    case 'easy': return 30;
    case 'medium': return 45;
    case 'hard': return 60;
    default: return 45;
  }
}

/**
 * Check if answer is correct
 */
export function checkAnswer(question: QuizQuestion, userAnswer: string | number): boolean {
  const correctAnswer = question.correctAnswer.toString().toLowerCase().trim();
  const userAnswerStr = userAnswer.toString().toLowerCase().trim();
  
  return correctAnswer === userAnswerStr;
}

/**
 * Get quiz statistics
 */
export function getQuizStatistics(sessions: QuizSession[]) {
  if (sessions.length === 0) {
    return {
      totalQuizzes: 0,
      averageScore: 0,
      bestScore: 0,
      totalTimeSpent: 0,
      favoriteCategory: 'general' as QuizCategory,
      strongestCategory: 'general' as QuizCategory,
      weakestCategory: 'general' as QuizCategory,
      streakCount: 0,
    };
  }
  
  const totalQuizzes = sessions.length;
  const averageScore = sessions.reduce((sum, session) => sum + session.percentage, 0) / totalQuizzes;
  const bestScore = Math.max(...sessions.map(session => session.percentage));
  const totalTimeSpent = sessions.reduce((sum, session) => sum + session.timeSpent, 0);
  
  // Calculate category statistics
  const categoryStats = sessions.reduce((stats, session) => {
    if (session.category !== 'mixed') {
      if (!stats[session.category]) {
        stats[session.category] = { count: 0, totalScore: 0 };
      }
      stats[session.category].count++;
      stats[session.category].totalScore += session.percentage;
    }
    return stats;
  }, {} as Record<QuizCategory, { count: number; totalScore: number }>);
  
  const favoriteCategory = Object.entries(categoryStats)
    .sort(([,a], [,b]) => b.count - a.count)[0]?.[0] as QuizCategory || 'general';
  
  const strongestCategory = Object.entries(categoryStats)
    .sort(([,a], [,b]) => (b.totalScore / b.count) - (a.totalScore / a.count))[0]?.[0] as QuizCategory || 'general';
  
  const weakestCategory = Object.entries(categoryStats)
    .sort(([,a], [,b]) => (a.totalScore / a.count) - (b.totalScore / b.count))[0]?.[0] as QuizCategory || 'general';
  
  // Calculate current streak
  let streakCount = 0;
  for (let i = sessions.length - 1; i >= 0; i--) {
    if (sessions[i].percentage >= 70) { // Consider 70% as passing
      streakCount++;
    } else {
      break;
    }
  }
  
  return {
    totalQuizzes,
    averageScore: Math.round(averageScore),
    bestScore,
    totalTimeSpent,
    favoriteCategory,
    strongestCategory,
    weakestCategory,
    streakCount,
  };
}

/**
 * Check for new achievements
 */
export function checkAchievements(
  sessions: QuizSession[],
  currentAchievements: Achievement[]
): Achievement[] {
  const newAchievements: Achievement[] = [];
  const unlockedIds = new Set(currentAchievements.filter(a => a.unlockedAt).map(a => a.id));
  
  // First quiz achievement
  if (sessions.length >= 1 && !unlockedIds.has('first_quiz')) {
    newAchievements.push({
      ...ACHIEVEMENTS_DATA.find(a => a.id === 'first_quiz')!,
      unlockedAt: new Date(),
      progress: 1,
    });
  }
  
  // Perfect score achievement
  const hasPerfectScore = sessions.some(session => session.percentage === 100);
  if (hasPerfectScore && !unlockedIds.has('perfect_score')) {
    newAchievements.push({
      ...ACHIEVEMENTS_DATA.find(a => a.id === 'perfect_score')!,
      unlockedAt: new Date(),
      progress: 1,
    });
  }
  
  // Quiz streak achievement
  const stats = getQuizStatistics(sessions);
  if (stats.streakCount >= 10 && !unlockedIds.has('quiz_streak')) {
    newAchievements.push({
      ...ACHIEVEMENTS_DATA.find(a => a.id === 'quiz_streak')!,
      unlockedAt: new Date(),
      progress: stats.streakCount,
    });
  }
  
  return newAchievements;
}

/**
 * Utility functions
 */
function shuffleArray<T>(array: T[]): T[] {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}

function getRandomCategory(): QuizCategory {
  const categories: QuizCategory[] = [
    'periodic-table', 'atomic-structure', 'chemical-bonding', 
    'compounds', 'reactions', 'general'
  ];
  return categories[Math.floor(Math.random() * categories.length)];
}

function getRandomDifficulty(): 'easy' | 'medium' | 'hard' {
  const difficulties = ['easy', 'medium', 'hard'] as const;
  return difficulties[Math.floor(Math.random() * difficulties.length)];
}
