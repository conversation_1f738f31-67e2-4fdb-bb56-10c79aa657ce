import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { Theme, UserPreferences } from '@/types';

interface ThemeState {
  // Theme state
  currentTheme: 'light' | 'dark' | 'auto';
  isDarkMode: boolean;
  
  // User preferences
  preferences: UserPreferences;
  
  // Theme definitions
  themes: {
    light: Theme;
    dark: Theme;
  };
  
  // Actions
  setTheme: (theme: 'light' | 'dark' | 'auto') => void;
  toggleTheme: () => void;
  updatePreferences: (preferences: Partial<UserPreferences>) => void;
  resetPreferences: () => void;
  
  // System theme detection
  detectSystemTheme: () => void;
  initializeTheme: () => void;
}

const defaultPreferences: UserPreferences = {
  theme: 'auto',
  language: 'en',
  notifications: true,
  soundEffects: true,
  animations: true,
  defaultQuizSettings: {
    questionCount: 10,
    difficulty: 'mixed',
    category: 'mixed',
    timeLimit: 60,
    hintsEnabled: true,
    randomOrder: true,
  },
};

const lightTheme: Theme = {
  name: 'light',
  colors: {
    primary: '#3B82F6',
    secondary: '#8B5CF6',
    background: '#FFFFFF',
    surface: '#F8FAFC',
    text: '#1F2937',
    textSecondary: '#6B7280',
    border: '#E5E7EB',
    success: '#10B981',
    warning: '#F59E0B',
    error: '#EF4444',
  },
};

const darkTheme: Theme = {
  name: 'dark',
  colors: {
    primary: '#60A5FA',
    secondary: '#A78BFA',
    background: '#111827',
    surface: '#1F2937',
    text: '#F9FAFB',
    textSecondary: '#D1D5DB',
    border: '#374151',
    success: '#34D399',
    warning: '#FBBF24',
    error: '#F87171',
  },
};

export const useThemeStore = create<ThemeState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        currentTheme: 'auto',
        isDarkMode: false,
        preferences: defaultPreferences,
        themes: {
          light: lightTheme,
          dark: darkTheme,
        },

        // Theme actions
        setTheme: (theme) => {
          set({ currentTheme: theme }, false, 'setTheme');
          
          // Update preferences
          const { preferences } = get();
          set({
            preferences: { ...preferences, theme }
          }, false, 'setTheme');
          
          // Apply theme
          get().applyTheme(theme);
        },

        toggleTheme: () => {
          const { currentTheme } = get();
          const newTheme = currentTheme === 'light' ? 'dark' : 'light';
          get().setTheme(newTheme);
        },

        updatePreferences: (newPreferences) => {
          const { preferences } = get();
          const updated = { ...preferences, ...newPreferences };
          
          set({ preferences: updated }, false, 'updatePreferences');
          
          // Apply theme if it changed
          if (newPreferences.theme && newPreferences.theme !== preferences.theme) {
            get().applyTheme(newPreferences.theme);
          }
        },

        resetPreferences: () => {
          set({ preferences: defaultPreferences }, false, 'resetPreferences');
          get().applyTheme(defaultPreferences.theme);
        },

        // System theme detection
        detectSystemTheme: () => {
          if (typeof window === 'undefined') return;
          
          const isDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
          set({ isDarkMode: isDark }, false, 'detectSystemTheme');
          
          const { currentTheme } = get();
          if (currentTheme === 'auto') {
            get().applyTheme('auto');
          }
        },

        initializeTheme: () => {
          const { currentTheme } = get();
          
          // Set up system theme listener
          if (typeof window !== 'undefined') {
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            
            const handleChange = (e: MediaQueryListEvent) => {
              set({ isDarkMode: e.matches }, false, 'systemThemeChange');
              
              const { currentTheme } = get();
              if (currentTheme === 'auto') {
                get().applyTheme('auto');
              }
            };
            
            mediaQuery.addEventListener('change', handleChange);
            
            // Initial detection
            set({ isDarkMode: mediaQuery.matches }, false, 'initializeTheme');
          }
          
          // Apply current theme
          get().applyTheme(currentTheme);
        },

        // Helper method to apply theme (not exposed in interface)
        applyTheme: (theme: 'light' | 'dark' | 'auto') => {
          if (typeof window === 'undefined') return;
          
          const { isDarkMode, themes } = get();
          let effectiveTheme: 'light' | 'dark';
          
          if (theme === 'auto') {
            effectiveTheme = isDarkMode ? 'dark' : 'light';
          } else {
            effectiveTheme = theme;
          }
          
          const themeColors = themes[effectiveTheme].colors;
          
          // Apply CSS custom properties
          const root = document.documentElement;
          Object.entries(themeColors).forEach(([key, value]) => {
            root.style.setProperty(`--color-${key}`, value);
          });
          
          // Update HTML class for Tailwind dark mode
          if (effectiveTheme === 'dark') {
            document.documentElement.classList.add('dark');
          } else {
            document.documentElement.classList.remove('dark');
          }
          
          // Update meta theme-color for mobile browsers
          const metaThemeColor = document.querySelector('meta[name="theme-color"]');
          if (metaThemeColor) {
            metaThemeColor.setAttribute('content', themeColors.primary);
          }
        },
      }),
      {
        name: 'theme-store',
        partialize: (state) => ({
          currentTheme: state.currentTheme,
          preferences: state.preferences,
        }),
      }
    ),
    { name: 'ThemeStore' }
  )
);

// Initialize theme on store creation
if (typeof window !== 'undefined') {
  useThemeStore.getState().initializeTheme();
}
