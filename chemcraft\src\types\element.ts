export interface Element {
  atomicNumber: number;
  symbol: string;
  name: string;
  atomicMass: number;
  electronConfiguration: string;
  electronegativity?: number;
  atomicRadius?: number;
  ionizationEnergy?: number;
  electronAffinity?: number;
  oxidationStates: number[];
  standardState: 'solid' | 'liquid' | 'gas';
  meltingPoint?: number; // in Kelvin
  boilingPoint?: number; // in Kelvin
  density?: number; // g/cm³
  groupBlock: string;
  yearDiscovered?: number;
  category: ElementCategory;
  color?: string; // hex color for visualization
  description?: string;
}

export type ElementCategory = 
  | 'alkali-metal'
  | 'alkaline-earth-metal'
  | 'transition-metal'
  | 'post-transition-metal'
  | 'metalloid'
  | 'nonmetal'
  | 'halogen'
  | 'noble-gas'
  | 'lanthanide'
  | 'actinide'
  | 'unknown';

export interface ElementPosition {
  period: number;
  group: number;
}

export interface ElementWithPosition extends Element {
  position: ElementPosition;
}

export interface ElementSelection {
  element: Element;
  quantity: number;
}

export interface ElementFilter {
  category?: ElementCategory;
  state?: 'solid' | 'liquid' | 'gas';
  period?: number;
  group?: number;
  searchTerm?: string;
}
