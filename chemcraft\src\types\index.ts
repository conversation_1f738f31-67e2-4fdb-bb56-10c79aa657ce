// Element types
export type {
  <PERSON><PERSON>,
  ElementCategory,
  ElementPosition,
  ElementWithPosition,
  ElementSelection,
  ElementFilter,
} from './element';

// Compound types
export type {
  CompoundElement,
  Compound,
  CompoundType,
  MixingResult,
  ChemicalReaction,
  ReactionType,
  MixingAttempt,
} from './compound';

// Quiz types
export type {
  QuizQuestion,
  QuestionType,
  QuizCategory,
  QuizAnswer,
  QuizSession,
  QuizStats,
  Achievement,
  QuizSettings,
} from './quiz';

// Common utility types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

export interface Theme {
  name: string;
  colors: {
    primary: string;
    secondary: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    border: string;
    success: string;
    warning: string;
    error: string;
  };
}

export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  preferences: UserPreferences;
  stats: QuizStats;
  createdAt: Date;
  lastLoginAt: Date;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  language: string;
  notifications: boolean;
  soundEffects: boolean;
  animations: boolean;
  defaultQuizSettings: QuizSettings;
}
