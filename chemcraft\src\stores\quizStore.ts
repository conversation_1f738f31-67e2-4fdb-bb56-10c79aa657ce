import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { 
  QuizQuestion, 
  QuizAnswer, 
  QuizSession, 
  QuizSettings, 
  QuizStats,
  Achievement 
} from '@/types';
import { DEFAULT_QUIZ_SETTINGS } from '@/constants';
import { 
  generateQuiz, 
  calculateQuizScore, 
  checkAnswer, 
  getQuizStatistics,
  checkAchievements,
  generateId 
} from '@/lib';

interface QuizState {
  // Current quiz session
  currentSession: QuizSession | null;
  currentQuestionIndex: number;
  currentAnswer: string | number | null;
  timeRemaining: number;
  hintsUsed: number;
  
  // Quiz settings
  settings: QuizSettings;
  
  // Quiz history and stats
  sessions: QuizSession[];
  stats: QuizStats;
  achievements: Achievement[];
  
  // UI state
  isLoading: boolean;
  showResults: boolean;
  showHint: boolean;
  
  // Actions
  updateSettings: (settings: Partial<QuizSettings>) => void;
  startQuiz: (customSettings?: Partial<QuizSettings>) => void;
  answerQuestion: (answer: string | number) => void;
  nextQuestion: () => void;
  useHint: () => void;
  pauseQuiz: () => void;
  resumeQuiz: () => void;
  endQuiz: () => void;
  restartQuiz: () => void;
  
  // Timer actions
  updateTimer: (timeRemaining: number) => void;
  
  // History actions
  loadQuizHistory: () => void;
  clearQuizHistory: () => void;
  
  // Achievement actions
  unlockAchievement: (achievementId: string) => void;
}

export const useQuizStore = create<QuizState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        currentSession: null,
        currentQuestionIndex: 0,
        currentAnswer: null,
        timeRemaining: 0,
        hintsUsed: 0,
        settings: DEFAULT_QUIZ_SETTINGS,
        sessions: [],
        stats: {
          totalQuizzes: 0,
          averageScore: 0,
          bestScore: 0,
          totalTimeSpent: 0,
          favoriteCategory: 'general',
          strongestCategory: 'general',
          weakestCategory: 'general',
          streakCount: 0,
          achievements: [],
        },
        achievements: [],
        isLoading: false,
        showResults: false,
        showHint: false,

        // Settings actions
        updateSettings: (newSettings) => {
          const { settings } = get();
          set(
            { settings: { ...settings, ...newSettings } },
            false,
            'updateSettings'
          );
        },

        // Quiz session actions
        startQuiz: (customSettings) => {
          const { settings } = get();
          const finalSettings = { ...settings, ...customSettings };
          
          set({ isLoading: true }, false, 'startQuiz');
          
          try {
            const questions = generateQuiz(finalSettings);
            const session: QuizSession = {
              id: generateId(),
              questions,
              answers: [],
              startTime: new Date(),
              score: 0,
              maxScore: questions.reduce((sum, q) => sum + q.points, 0),
              percentage: 0,
              timeSpent: 0,
              difficulty: finalSettings.difficulty,
              category: finalSettings.category,
              completed: false,
            };

            set({
              currentSession: session,
              currentQuestionIndex: 0,
              currentAnswer: null,
              timeRemaining: finalSettings.timeLimit || 60,
              hintsUsed: 0,
              isLoading: false,
              showResults: false,
              showHint: false,
            }, false, 'startQuiz');
          } catch (error) {
            console.error('Failed to start quiz:', error);
            set({ isLoading: false }, false, 'startQuiz');
          }
        },

        answerQuestion: (answer) => {
          const { currentSession, currentQuestionIndex, hintsUsed, timeRemaining } = get();
          if (!currentSession) return;

          const question = currentSession.questions[currentQuestionIndex];
          const isCorrect = checkAnswer(question, answer);
          const timeSpent = (question.timeLimit || 60) - timeRemaining;

          const quizAnswer: QuizAnswer = {
            questionId: question.id,
            userAnswer: answer,
            isCorrect,
            timeSpent,
            hintsUsed,
            timestamp: new Date(),
          };

          const updatedAnswers = [...currentSession.answers, quizAnswer];
          const updatedSession = { ...currentSession, answers: updatedAnswers };

          set({
            currentSession: updatedSession,
            currentAnswer: answer,
            showHint: false,
          }, false, 'answerQuestion');
        },

        nextQuestion: () => {
          const { currentSession, currentQuestionIndex, settings } = get();
          if (!currentSession) return;

          const nextIndex = currentQuestionIndex + 1;
          
          if (nextIndex >= currentSession.questions.length) {
            // Quiz completed
            get().endQuiz();
          } else {
            // Move to next question
            const nextQuestion = currentSession.questions[nextIndex];
            set({
              currentQuestionIndex: nextIndex,
              currentAnswer: null,
              timeRemaining: settings.timeLimit || nextQuestion.timeLimit || 60,
              hintsUsed: 0,
              showHint: false,
            }, false, 'nextQuestion');
          }
        },

        useHint: () => {
          const { hintsUsed, settings } = get();
          if (!settings.hintsEnabled) return;

          set({
            hintsUsed: hintsUsed + 1,
            showHint: true,
          }, false, 'useHint');
        },

        pauseQuiz: () => {
          // Implementation for pausing quiz
          // This could involve stopping timers, etc.
        },

        resumeQuiz: () => {
          // Implementation for resuming quiz
        },

        endQuiz: () => {
          const { currentSession, sessions, achievements } = get();
          if (!currentSession) return;

          const endTime = new Date();
          const totalTimeSpent = Math.floor((endTime.getTime() - currentSession.startTime.getTime()) / 1000);
          const scoreData = calculateQuizScore(currentSession.answers);

          const completedSession: QuizSession = {
            ...currentSession,
            endTime,
            timeSpent: totalTimeSpent,
            score: scoreData.score,
            percentage: scoreData.percentage,
            completed: true,
          };

          const updatedSessions = [...sessions, completedSession];
          const newStats = getQuizStatistics(updatedSessions);
          const newAchievements = checkAchievements(updatedSessions, achievements);

          set({
            currentSession: completedSession,
            sessions: updatedSessions,
            stats: { ...newStats, achievements: [...achievements, ...newAchievements] },
            achievements: [...achievements, ...newAchievements],
            showResults: true,
          }, false, 'endQuiz');
        },

        restartQuiz: () => {
          const { settings } = get();
          get().startQuiz(settings);
        },

        // Timer actions
        updateTimer: (timeRemaining) => {
          set({ timeRemaining }, false, 'updateTimer');
          
          if (timeRemaining <= 0) {
            // Auto-submit current question or move to next
            const { currentAnswer } = get();
            if (currentAnswer !== null) {
              get().nextQuestion();
            }
          }
        },

        // History actions
        loadQuizHistory: () => {
          const { sessions } = get();
          const stats = getQuizStatistics(sessions);
          set({ stats }, false, 'loadQuizHistory');
        },

        clearQuizHistory: () => {
          set({
            sessions: [],
            stats: {
              totalQuizzes: 0,
              averageScore: 0,
              bestScore: 0,
              totalTimeSpent: 0,
              favoriteCategory: 'general',
              strongestCategory: 'general',
              weakestCategory: 'general',
              streakCount: 0,
              achievements: [],
            },
          }, false, 'clearQuizHistory');
        },

        // Achievement actions
        unlockAchievement: (achievementId) => {
          const { achievements } = get();
          const updated = achievements.map(achievement =>
            achievement.id === achievementId
              ? { ...achievement, unlockedAt: new Date() }
              : achievement
          );
          set({ achievements: updated }, false, 'unlockAchievement');
        },
      }),
      {
        name: 'quiz-store',
        partialize: (state) => ({
          settings: state.settings,
          sessions: state.sessions,
          stats: state.stats,
          achievements: state.achievements,
        }),
      }
    ),
    { name: 'QuizStore' }
  )
);
