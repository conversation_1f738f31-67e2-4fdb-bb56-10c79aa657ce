// Element constants
export {
  <PERSON>LEMENTS,
  ELEMENT_COLORS,
  ELEMENT_POSITIONS,
  PERIOD_COUNT,
  GROUP_COUNT,
} from './elements';

// Category constants
export {
  ELEMENT_CATEGORIES,
  COMPOUND_TYPES,
  QUIZ_CATEGORIES,
  DIF<PERSON>CULTY_LEVELS,
} from './categories';

// Quiz constants
export {
  SAMPLE_QUIZ_QUESTIONS,
  ACHIEVEMENTS_DATA,
  DEFAULT_QUIZ_SETTINGS,
} from './quizData';

// App constants
export const APP_NAME = 'ChemCraft';
export const APP_VERSION = '1.0.0';
export const APP_DESCRIPTION = 'Interactive Chemistry Learning Platform';

// API endpoints (if needed)
export const API_ENDPOINTS = {
  ELEMENTS: '/api/elements',
  COMPOUNDS: '/api/compounds',
  QUIZ: '/api/quiz',
  USER: '/api/user',
} as const;

// Local storage keys
export const STORAGE_KEYS = {
  THEME: 'chemcraft_theme',
  USER_PREFERENCES: 'chemcraft_preferences',
  QUIZ_PROGRESS: 'chemcraft_quiz_progress',
  ACHIEVEMENTS: 'chemcraft_achievements',
} as const;

// Animation durations (in milliseconds)
export const ANIMATIONS = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500,
} as const;

// Breakpoints for responsive design
export const BREAKPOINTS = {
  SM: 640,
  MD: 768,
  LG: 1024,
  XL: 1280,
  '2XL': 1536,
} as const;
