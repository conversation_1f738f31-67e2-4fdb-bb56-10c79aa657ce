import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { Element, ElementFilter, ElementSelection, MixingResult } from '@/types';
import { ELEMENTS } from '@/constants';
import { filterElements, searchElements, mixElements } from '@/lib';

interface ElementState {
  // Element data
  elements: Element[];
  selectedElement: Element | null;
  filteredElements: Element[];
  searchQuery: string;
  filter: ElementFilter;
  
  // Mixer state
  selectedElements: ElementSelection[];
  mixingResult: MixingResult | null;
  isLoading: boolean;
  
  // Actions
  setSelectedElement: (element: Element | null) => void;
  setSearchQuery: (query: string) => void;
  setFilter: (filter: ElementFilter) => void;
  clearFilter: () => void;
  
  // Mixer actions
  addElementToMixer: (element: Element, quantity?: number) => void;
  removeElementFromMixer: (elementId: number) => void;
  updateElementQuantity: (elementId: number, quantity: number) => void;
  clearMixer: () => void;
  performMixing: () => void;
  clearMixingResult: () => void;
  
  // Utility actions
  loadElements: () => Promise<void>;
  refreshElements: () => void;
}

export const useElementStore = create<ElementState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        elements: ELEMENTS,
        selectedElement: null,
        filteredElements: ELEMENTS,
        searchQuery: '',
        filter: {},
        selectedElements: [],
        mixingResult: null,
        isLoading: false,

        // Element actions
        setSelectedElement: (element) => {
          set({ selectedElement: element }, false, 'setSelectedElement');
        },

        setSearchQuery: (query) => {
          set({ searchQuery: query }, false, 'setSearchQuery');
          get().refreshElements();
        },

        setFilter: (filter) => {
          set({ filter }, false, 'setFilter');
          get().refreshElements();
        },

        clearFilter: () => {
          set({ filter: {}, searchQuery: '' }, false, 'clearFilter');
          get().refreshElements();
        },

        // Mixer actions
        addElementToMixer: (element, quantity = 1) => {
          const { selectedElements } = get();
          const existingIndex = selectedElements.findIndex(
            sel => sel.element.atomicNumber === element.atomicNumber
          );

          if (existingIndex >= 0) {
            // Update existing element quantity
            const updated = [...selectedElements];
            updated[existingIndex].quantity += quantity;
            set({ selectedElements: updated }, false, 'addElementToMixer');
          } else {
            // Add new element
            const newSelection: ElementSelection = { element, quantity };
            set(
              { selectedElements: [...selectedElements, newSelection] },
              false,
              'addElementToMixer'
            );
          }
        },

        removeElementFromMixer: (elementId) => {
          const { selectedElements } = get();
          const filtered = selectedElements.filter(
            sel => sel.element.atomicNumber !== elementId
          );
          set({ selectedElements: filtered }, false, 'removeElementFromMixer');
        },

        updateElementQuantity: (elementId, quantity) => {
          const { selectedElements } = get();
          if (quantity <= 0) {
            get().removeElementFromMixer(elementId);
            return;
          }

          const updated = selectedElements.map(sel =>
            sel.element.atomicNumber === elementId
              ? { ...sel, quantity }
              : sel
          );
          set({ selectedElements: updated }, false, 'updateElementQuantity');
        },

        clearMixer: () => {
          set(
            { selectedElements: [], mixingResult: null },
            false,
            'clearMixer'
          );
        },

        performMixing: () => {
          const { selectedElements } = get();
          set({ isLoading: true }, false, 'performMixing');

          // Simulate async operation
          setTimeout(() => {
            const compoundElements = selectedElements.map(sel => ({
              element: sel.element,
              count: sel.quantity,
            }));

            const result = mixElements(compoundElements);
            set(
              { mixingResult: result, isLoading: false },
              false,
              'performMixing'
            );
          }, 1000);
        },

        clearMixingResult: () => {
          set({ mixingResult: null }, false, 'clearMixingResult');
        },

        // Utility actions
        loadElements: async () => {
          set({ isLoading: true }, false, 'loadElements');
          
          try {
            // In a real app, this would fetch from an API
            // For now, we'll use the static data
            await new Promise(resolve => setTimeout(resolve, 500));
            
            set(
              { elements: ELEMENTS, isLoading: false },
              false,
              'loadElements'
            );
            get().refreshElements();
          } catch (error) {
            console.error('Failed to load elements:', error);
            set({ isLoading: false }, false, 'loadElements');
          }
        },

        refreshElements: () => {
          const { elements, searchQuery, filter } = get();
          let filtered = elements;

          // Apply search query
          if (searchQuery.trim()) {
            filtered = searchElements(searchQuery);
          }

          // Apply filters
          if (Object.keys(filter).length > 0) {
            filtered = filterElements({ ...filter, searchTerm: searchQuery });
          }

          set({ filteredElements: filtered }, false, 'refreshElements');
        },
      }),
      {
        name: 'element-store',
        partialize: (state) => ({
          selectedElements: state.selectedElements,
          filter: state.filter,
          searchQuery: state.searchQuery,
        }),
      }
    ),
    { name: 'ElementStore' }
  )
);
